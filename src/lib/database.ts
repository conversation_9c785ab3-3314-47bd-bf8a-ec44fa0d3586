import Dexie, { Table } from 'dexie';
import { TodoItem, TodoDetail } from '@/types/todo';

// IndexedDB 数据库类
export class TodoDatabase extends Dexie {
  // 定义表
  todos!: Table<TodoItem>;

  constructor() {
    super('TodoListDatabase');
    
    // 定义数据库 schema
    this.version(1).stores({
      todos: 'id, name, completed, createdAt, *details.id, *details.content, *details.completed'
    });

    // 数据迁移和升级处理
    this.version(1).upgrade(async (tx) => {
      // 如果需要从 localStorage 迁移数据
      const localStorageData = localStorage.getItem('todos');
      if (localStorageData) {
        try {
          const todos = JSON.parse(localStorageData);
          const migratedTodos = todos.map((todo: any) => {
            // 处理旧格式的 details（字符串数组）
            let details = todo.details || [];
            if (details.length > 0 && typeof details[0] === 'string') {
              details = details.map((content: string, index: number) => ({
                id: `${todo.id}-detail-${index}`,
                content,
                completed: false
              }));
            }
            
            return {
              ...todo,
              details,
              createdAt: new Date(todo.createdAt)
            };
          });

          // 批量插入到 IndexedDB
          await tx.table('todos').bulkAdd(migratedTodos);
          
          // 迁移完成后清除 localStorage
          localStorage.removeItem('todos');
          console.log('数据已从 localStorage 迁移到 IndexedDB');
        } catch (error) {
          console.error('数据迁移失败:', error);
        }
      }
    });
  }
}

// 创建数据库实例
export const db = new TodoDatabase();

// 数据库操作类
export class TodoService {
  // 获取所有待办事项
  static async getAllTodos(): Promise<TodoItem[]> {
    try {
      const todos = await db.todos.orderBy('createdAt').reverse().toArray();
      return todos;
    } catch (error) {
      console.error('获取待办事项失败:', error);
      return [];
    }
  }

  // 添加待办事项
  static async addTodo(name: string, details: string[] = []): Promise<string> {
    try {
      const todoDetails: TodoDetail[] = details.map((content, index) => ({
        id: `${Date.now()}-detail-${index}`,
        content,
        completed: false
      }));
      
      const newTodo: TodoItem = {
        id: Date.now().toString(),
        name,
        completed: false,
        details: todoDetails,
        createdAt: new Date()
      };

      await db.todos.add(newTodo);
      return newTodo.id;
    } catch (error) {
      console.error('添加待办事项失败:', error);
      throw error;
    }
  }

  // 更新待办事项
  static async updateTodo(id: string, updates: Partial<TodoItem>): Promise<void> {
    try {
      await db.todos.update(id, updates);
    } catch (error) {
      console.error('更新待办事项失败:', error);
      throw error;
    }
  }

  // 删除待办事项
  static async deleteTodo(id: string): Promise<void> {
    try {
      await db.todos.delete(id);
    } catch (error) {
      console.error('删除待办事项失败:', error);
      throw error;
    }
  }

  // 切换待办事项完成状态
  static async toggleTodo(id: string): Promise<void> {
    try {
      const todo = await db.todos.get(id);
      if (!todo) return;

      // 如果没有明细，直接切换完成状态
      if (todo.details.length === 0) {
        await db.todos.update(id, { completed: !todo.completed });
        return;
      }

      // 如果有明细，切换所有明细的完成状态
      const newCompleted = !TodoService.calculateCompletedStatus(todo);
      const updatedDetails = todo.details.map(detail => ({
        ...detail,
        completed: newCompleted
      }));

      await db.todos.update(id, {
        details: updatedDetails,
        completed: newCompleted
      });
    } catch (error) {
      console.error('切换待办事项状态失败:', error);
      throw error;
    }
  }

  // 切换明细完成状态
  static async toggleDetailCompletion(todoId: string, detailId: string): Promise<void> {
    try {
      const todo = await db.todos.get(todoId);
      if (!todo) return;

      const updatedDetails = todo.details.map(detail =>
        detail.id === detailId 
          ? { ...detail, completed: !detail.completed }
          : detail
      );

      const completed = TodoService.calculateCompletedStatusFromDetails(updatedDetails);
      
      await db.todos.update(todoId, {
        details: updatedDetails,
        completed
      });
    } catch (error) {
      console.error('切换明细状态失败:', error);
      throw error;
    }
  }

  // 添加明细到现有事项
  static async addDetailToTodo(todoId: string, content: string): Promise<void> {
    try {
      const todo = await db.todos.get(todoId);
      if (!todo) return;

      const newDetail: TodoDetail = {
        id: `${todoId}-detail-${Date.now()}`,
        content,
        completed: false
      };

      const updatedDetails = [...todo.details, newDetail];
      const completed = TodoService.calculateCompletedStatusFromDetails(updatedDetails);

      await db.todos.update(todoId, {
        details: updatedDetails,
        completed
      });
    } catch (error) {
      console.error('添加明细失败:', error);
      throw error;
    }
  }

  // 删除明细
  static async removeDetailFromTodo(todoId: string, detailId: string): Promise<void> {
    try {
      const todo = await db.todos.get(todoId);
      if (!todo) return;

      const updatedDetails = todo.details.filter(detail => detail.id !== detailId);
      const completed = TodoService.calculateCompletedStatusFromDetails(updatedDetails);

      await db.todos.update(todoId, {
        details: updatedDetails,
        completed
      });
    } catch (error) {
      console.error('删除明细失败:', error);
      throw error;
    }
  }

  // 更新明细内容
  static async updateDetailContent(todoId: string, detailId: string, content: string): Promise<void> {
    try {
      const todo = await db.todos.get(todoId);
      if (!todo) return;

      const updatedDetails = todo.details.map(detail =>
        detail.id === detailId 
          ? { ...detail, content }
          : detail
      );

      await db.todos.update(todoId, { details: updatedDetails });
    } catch (error) {
      console.error('更新明细内容失败:', error);
      throw error;
    }
  }

  // 计算完成状态（基于明细）
  static calculateCompletedStatus(todo: TodoItem): boolean {
    if (todo.details.length === 0) {
      return todo.completed;
    }
    return todo.details.every(detail => detail.completed);
  }

  // 从明细数组计算完成状态
  static calculateCompletedStatusFromDetails(details: TodoDetail[]): boolean {
    if (details.length === 0) {
      return false;
    }
    return details.every(detail => detail.completed);
  }

  // 获取统计信息
  static async getStats() {
    try {
      const todos = await db.todos.toArray();
      const total = todos.length;
      const completed = todos.filter(todo => TodoService.calculateCompletedStatus(todo)).length;
      const pending = total - completed;

      return { total, completed, pending };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      return { total: 0, completed: 0, pending: 0 };
    }
  }

  // 清空所有数据
  static async clearAllTodos(): Promise<void> {
    try {
      await db.todos.clear();
    } catch (error) {
      console.error('清空数据失败:', error);
      throw error;
    }
  }
}
