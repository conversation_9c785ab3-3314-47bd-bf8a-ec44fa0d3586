'use client';

import { useState, useEffect, useCallback } from 'react';
import { TodoItem } from '@/types/todo';
import { TodoService } from '@/lib/database';

export const useIndexedDBTodos = () => {
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [selectedTodoId, setSelectedTodoId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // 加载所有待办事项
  const loadTodos = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const todoList = await TodoService.getAllTodos();
      setTodos(todoList);
    } catch (err) {
      setError('加载待办事项失败');
      console.error('加载待办事项失败:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化加载数据
  useEffect(() => {
    loadTodos();
  }, [loadTodos]);

  // 添加待办事项
  const addTodo = useCallback(async (name: string, details: string[] = []) => {
    try {
      setError(null);
      await TodoService.addTodo(name, details);
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('添加待办事项失败');
      console.error('添加待办事项失败:', err);
    }
  }, [loadTodos]);

  // 切换待办事项完成状态
  const toggleTodo = useCallback(async (id: string) => {
    try {
      setError(null);
      await TodoService.toggleTodo(id);
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('切换待办事项状态失败');
      console.error('切换待办事项状态失败:', err);
    }
  }, [loadTodos]);

  // 删除待办事项
  const deleteTodo = useCallback(async (id: string) => {
    try {
      setError(null);
      await TodoService.deleteTodo(id);
      if (selectedTodoId === id) {
        setSelectedTodoId(null);
      }
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('删除待办事项失败');
      console.error('删除待办事项失败:', err);
    }
  }, [selectedTodoId, loadTodos]);

  // 更新待办事项
  const updateTodo = useCallback(async (id: string, updates: Partial<TodoItem>) => {
    try {
      setError(null);
      await TodoService.updateTodo(id, updates);
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('更新待办事项失败');
      console.error('更新待办事项失败:', err);
    }
  }, [loadTodos]);

  // 切换明细完成状态
  const toggleDetailCompletion = useCallback(async (todoId: string, detailId: string) => {
    try {
      setError(null);
      await TodoService.toggleDetailCompletion(todoId, detailId);
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('切换明细状态失败');
      console.error('切换明细状态失败:', err);
    }
  }, [loadTodos]);

  // 添加明细到现有事项
  const addDetailToTodo = useCallback(async (todoId: string, content: string) => {
    try {
      setError(null);
      await TodoService.addDetailToTodo(todoId, content);
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('添加明细失败');
      console.error('添加明细失败:', err);
    }
  }, [loadTodos]);

  // 删除明细
  const removeDetailFromTodo = useCallback(async (todoId: string, detailId: string) => {
    try {
      setError(null);
      await TodoService.removeDetailFromTodo(todoId, detailId);
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('删除明细失败');
      console.error('删除明细失败:', err);
    }
  }, [loadTodos]);

  // 更新明细内容
  const updateDetailContent = useCallback(async (todoId: string, detailId: string, content: string) => {
    try {
      setError(null);
      await TodoService.updateDetailContent(todoId, detailId, content);
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('更新明细内容失败');
      console.error('更新明细内容失败:', err);
    }
  }, [loadTodos]);

  // 计算完成状态
  const calculateCompletedStatus = useCallback((todo: TodoItem): boolean => {
    return TodoService.calculateCompletedStatus(todo);
  }, []);

  // 清空所有数据
  const clearAllTodos = useCallback(async () => {
    try {
      setError(null);
      await TodoService.clearAllTodos();
      setSelectedTodoId(null);
      await loadTodos(); // 重新加载数据
    } catch (err) {
      setError('清空数据失败');
      console.error('清空数据失败:', err);
    }
  }, [loadTodos]);

  // 获取选中的待办事项
  const selectedTodo = todos.find(todo => todo.id === selectedTodoId) || null;

  return {
    // 数据
    todos,
    selectedTodo,
    selectedTodoId,
    loading,
    error,
    
    // 操作方法
    setSelectedTodoId,
    addTodo,
    toggleTodo,
    deleteTodo,
    updateTodo,
    toggleDetailCompletion,
    addDetailToTodo,
    removeDetailFromTodo,
    updateDetailContent,
    calculateCompletedStatus,
    clearAllTodos,
    
    // 工具方法
    loadTodos
  };
};
