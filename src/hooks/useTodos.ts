'use client';

import { useState, useEffect } from 'react';
import { TodoItem, TodoDetail } from '@/types/todo';

export const useTodos = () => {
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [selectedTodoId, setSelectedTodoId] = useState<string | null>(null);

  // 计算事项的完成状态（基于明细）
  const calculateCompletedStatus = (todo: TodoItem): boolean => {
    if (todo.details.length === 0) {
      return todo.completed;
    }
    return todo.details.every(detail => detail.completed);
  };

  // 从 localStorage 加载数据
  useEffect(() => {
    const savedTodos = localStorage.getItem('todos');
    if (savedTodos) {
      const parsedTodos = JSON.parse(savedTodos).map((todo: any) => {
        // 数据迁移：处理旧格式的 details（字符串数组）
        let details = todo.details || [];
        if (details.length > 0 && typeof details[0] === 'string') {
          details = details.map((content: string, index: number) => ({
            id: `${todo.id}-detail-${index}`,
            content,
            completed: false
          }));
        }

        const todoItem = {
          ...todo,
          details,
          createdAt: new Date(todo.createdAt)
        };

        // 重新计算完成状态
        todoItem.completed = calculateCompletedStatus(todoItem);
        return todoItem;
      });
      setTodos(parsedTodos);
    }
  }, []);

  // 保存到 localStorage
  useEffect(() => {
    localStorage.setItem('todos', JSON.stringify(todos));
  }, [todos]);

  const addTodo = (name: string, details: string[] = []) => {
    const todoDetails: TodoDetail[] = details.map((content, index) => ({
      id: `${Date.now()}-detail-${index}`,
      content,
      completed: false
    }));

    const newTodo: TodoItem = {
      id: Date.now().toString(),
      name,
      completed: details.length === 0 ? false : false, // 如果有明细，初始状态为未完成
      details: todoDetails,
      createdAt: new Date()
    };
    setTodos(prev => [...prev, newTodo]);
  };

  const toggleTodo = (id: string) => {
    setTodos(prev =>
      prev.map(todo => {
        if (todo.id === id) {
          // 如果没有明细，直接切换完成状态
          if (todo.details.length === 0) {
            return { ...todo, completed: !todo.completed };
          }
          // 如果有明细，切换所有明细的完成状态
          const newCompleted = !calculateCompletedStatus(todo);
          const updatedDetails = todo.details.map(detail => ({
            ...detail,
            completed: newCompleted
          }));
          return {
            ...todo,
            details: updatedDetails,
            completed: newCompleted
          };
        }
        return todo;
      })
    );
  };

  const deleteTodo = (id: string) => {
    setTodos(prev => prev.filter(todo => todo.id !== id));
    if (selectedTodoId === id) {
      setSelectedTodoId(null);
    }
  };

  const updateTodo = (id: string, updates: Partial<TodoItem>) => {
    setTodos(prev =>
      prev.map(todo => {
        if (todo.id === id) {
          const updatedTodo = { ...todo, ...updates };
          // 重新计算完成状态
          updatedTodo.completed = calculateCompletedStatus(updatedTodo);
          return updatedTodo;
        }
        return todo;
      })
    );
  };

  // 切换明细完成状态
  const toggleDetailCompletion = (todoId: string, detailId: string) => {
    setTodos(prev =>
      prev.map(todo => {
        if (todo.id === todoId) {
          const updatedDetails = todo.details.map(detail =>
            detail.id === detailId
              ? { ...detail, completed: !detail.completed }
              : detail
          );
          const updatedTodo = { ...todo, details: updatedDetails };
          updatedTodo.completed = calculateCompletedStatus(updatedTodo);
          return updatedTodo;
        }
        return todo;
      })
    );
  };

  // 添加明细到现有事项
  const addDetailToTodo = (todoId: string, content: string) => {
    setTodos(prev =>
      prev.map(todo => {
        if (todo.id === todoId) {
          const newDetail: TodoDetail = {
            id: `${todoId}-detail-${Date.now()}`,
            content,
            completed: false
          };
          const updatedDetails = [...todo.details, newDetail];
          const updatedTodo = { ...todo, details: updatedDetails };
          updatedTodo.completed = calculateCompletedStatus(updatedTodo);
          return updatedTodo;
        }
        return todo;
      })
    );
  };

  // 删除明细
  const removeDetailFromTodo = (todoId: string, detailId: string) => {
    setTodos(prev =>
      prev.map(todo => {
        if (todo.id === todoId) {
          const updatedDetails = todo.details.filter(detail => detail.id !== detailId);
          const updatedTodo = { ...todo, details: updatedDetails };
          updatedTodo.completed = calculateCompletedStatus(updatedTodo);
          return updatedTodo;
        }
        return todo;
      })
    );
  };

  // 更新明细内容
  const updateDetailContent = (todoId: string, detailId: string, content: string) => {
    setTodos(prev =>
      prev.map(todo => {
        if (todo.id === todoId) {
          const updatedDetails = todo.details.map(detail =>
            detail.id === detailId
              ? { ...detail, content }
              : detail
          );
          return { ...todo, details: updatedDetails };
        }
        return todo;
      })
    );
  };

  const selectedTodo = todos.find(todo => todo.id === selectedTodoId);

  return {
    todos,
    selectedTodo,
    selectedTodoId,
    setSelectedTodoId,
    addTodo,
    toggleTodo,
    deleteTodo,
    updateTodo,
    toggleDetailCompletion,
    addDetailToTodo,
    removeDetailFromTodo,
    updateDetailContent,
    calculateCompletedStatus
  };
};
