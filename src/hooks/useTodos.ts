'use client';

import { useState, useEffect } from 'react';
import { TodoItem } from '@/types/todo';

export const useTodos = () => {
  const [todos, setTodos] = useState<TodoItem[]>([]);
  const [selectedTodoId, setSelectedTodoId] = useState<string | null>(null);

  // 从 localStorage 加载数据
  useEffect(() => {
    const savedTodos = localStorage.getItem('todos');
    if (savedTodos) {
      const parsedTodos = JSON.parse(savedTodos).map((todo: any) => ({
        ...todo,
        createdAt: new Date(todo.createdAt)
      }));
      setTodos(parsedTodos);
    }
  }, []);

  // 保存到 localStorage
  useEffect(() => {
    localStorage.setItem('todos', JSON.stringify(todos));
  }, [todos]);

  const addTodo = (name: string, details: string[] = []) => {
    const newTodo: TodoItem = {
      id: Date.now().toString(),
      name,
      completed: false,
      details,
      createdAt: new Date()
    };
    setTodos(prev => [...prev, newTodo]);
  };

  const toggleTodo = (id: string) => {
    setTodos(prev => 
      prev.map(todo => 
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      )
    );
  };

  const deleteTodo = (id: string) => {
    setTodos(prev => prev.filter(todo => todo.id !== id));
    if (selectedTodoId === id) {
      setSelectedTodoId(null);
    }
  };

  const updateTodo = (id: string, updates: Partial<TodoItem>) => {
    setTodos(prev => 
      prev.map(todo => 
        todo.id === id ? { ...todo, ...updates } : todo
      )
    );
  };

  const selectedTodo = todos.find(todo => todo.id === selectedTodoId);

  return {
    todos,
    selectedTodo,
    selectedTodoId,
    setSelectedTodoId,
    addTodo,
    toggleTodo,
    deleteTodo,
    updateTodo
  };
};
