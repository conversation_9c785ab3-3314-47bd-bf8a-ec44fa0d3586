'use client';

import { useState } from 'react';
import { TodoItem as TodoItemType } from '@/types/todo';

interface TodoItemProps {
  todo: TodoItemType;
  isSelected: boolean;
  onToggle: (id: string) => void;
  onSelect: (id: string) => void;
  onDelete: (id: string) => void;
  onToggleDetail: (todoId: string, detailId: string) => void;
  onAddDetail: (todoId: string, content: string) => void;
  onRemoveDetail: (todoId: string, detailId: string) => void;
  onUpdateDetail: (todoId: string, detailId: string, content: string) => void;
  calculateCompletedStatus: (todo: TodoItemType) => boolean;
}

export default function TodoItem({
  todo,
  isSelected,
  onToggle,
  onSelect,
  onDelete,
  onToggleDetail,
  onAddDetail,
  onRemoveDetail,
  onUpdateDetail,
  calculateCompletedStatus
}: TodoItemProps) {
  const [newDetailContent, setNewDetailContent] = useState('');
  const [editingDetailId, setEditingDetailId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState('');

  const actualCompleted = calculateCompletedStatus(todo);

  const handleAddDetail = () => {
    if (newDetailContent.trim()) {
      onAddDetail(todo.id, newDetailContent.trim());
      setNewDetailContent('');
    }
  };

  const handleEditDetail = (detailId: string, currentContent: string) => {
    setEditingDetailId(detailId);
    setEditingContent(currentContent);
  };

  const handleSaveEdit = () => {
    if (editingDetailId && editingContent.trim()) {
      onUpdateDetail(todo.id, editingDetailId, editingContent.trim());
    }
    setEditingDetailId(null);
    setEditingContent('');
  };

  const handleCancelEdit = () => {
    setEditingDetailId(null);
    setEditingContent('');
  };
  return (
    <div 
      className={`
        bg-white rounded-apple shadow-apple border border-apple-gray-200
        transition-all duration-200 ease-in-out cursor-pointer
        ${isSelected ? 'ring-2 ring-apple-blue ring-opacity-50 shadow-apple-lg' : 'hover:shadow-apple-lg'}
      `}
      onClick={() => onSelect(todo.id)}
    >
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1">
          {/* 苹果风格复选框 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggle(todo.id);
            }}
            className={`
              w-6 h-6 rounded-full border-2 flex items-center justify-center
              transition-all duration-200 ease-in-out
              ${actualCompleted
                ? 'bg-apple-blue border-apple-blue'
                : 'border-apple-gray-300 hover:border-apple-blue'
              }
            `}
          >
            {actualCompleted && (
              <svg 
                className="w-3 h-3 text-white" 
                fill="currentColor" 
                viewBox="0 0 20 20"
              >
                <path 
                  fillRule="evenodd" 
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                  clipRule="evenodd" 
                />
              </svg>
            )}
          </button>
          
          {/* 事项名称 */}
          <span 
            className={`
              text-lg font-medium transition-all duration-200
              ${actualCompleted
                ? 'text-apple-gray-500 line-through'
                : 'text-apple-gray-900'
              }
            `}
          >
            {todo.name}
          </span>
        </div>

        {/* 删除按钮 */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete(todo.id);
          }}
          className="
            w-8 h-8 rounded-full bg-red-500 text-white
            flex items-center justify-center opacity-0 hover:opacity-100
            transition-opacity duration-200 ease-in-out
            hover:bg-red-600
          "
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path 
              fillRule="evenodd" 
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
              clipRule="evenodd" 
            />
          </svg>
        </button>
      </div>

      {/* 明细展开区域 */}
      {isSelected && (
        <div className="border-t border-apple-gray-200 px-4 py-3 bg-apple-gray-50">
          <div className="flex items-center justify-between mb-3">
            <h4 className="text-sm font-medium text-apple-gray-700">
              明细 ({todo.details.length})
            </h4>
            <div className="text-xs text-apple-gray-500">
              已完成: {todo.details.filter(d => d.completed).length} / {todo.details.length}
            </div>
          </div>

          {/* 明细列表 */}
          {todo.details.length > 0 && (
            <ul className="space-y-2 mb-3">
              {todo.details.map((detail, index) => (
                <li
                  key={detail.id}
                  className="flex items-center space-x-2 group"
                >
                  {/* 明细复选框 */}
                  <button
                    onClick={() => onToggleDetail(todo.id, detail.id)}
                    className={`
                      w-4 h-4 rounded border flex items-center justify-center
                      transition-all duration-200 ease-in-out flex-shrink-0
                      ${detail.completed
                        ? 'bg-apple-blue border-apple-blue'
                        : 'border-apple-gray-300 hover:border-apple-blue'
                      }
                    `}
                  >
                    {detail.completed && (
                      <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </button>

                  <span className="text-apple-blue font-medium text-xs w-4">{index + 1}.</span>

                  {/* 明细内容 */}
                  {editingDetailId === detail.id ? (
                    <div className="flex-1 flex items-center space-x-2">
                      <input
                        type="text"
                        value={editingContent}
                        onChange={(e) => setEditingContent(e.target.value)}
                        className="flex-1 px-2 py-1 text-xs border border-apple-gray-300 rounded focus:ring-1 focus:ring-apple-blue focus:border-transparent outline-none"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') handleSaveEdit();
                          if (e.key === 'Escape') handleCancelEdit();
                        }}
                        autoFocus
                      />
                      <button
                        onClick={handleSaveEdit}
                        className="text-apple-blue hover:text-blue-600 text-xs"
                      >
                        保存
                      </button>
                      <button
                        onClick={handleCancelEdit}
                        className="text-apple-gray-500 hover:text-apple-gray-700 text-xs"
                      >
                        取消
                      </button>
                    </div>
                  ) : (
                    <div className="flex-1 flex items-center justify-between">
                      <span
                        className={`
                          text-xs transition-all duration-200 cursor-pointer
                          ${detail.completed
                            ? 'text-apple-gray-500 line-through'
                            : 'text-apple-gray-700'
                          }
                        `}
                        onClick={() => handleEditDetail(detail.id, detail.content)}
                      >
                        {detail.content}
                      </span>

                      {/* 明细操作按钮 */}
                      <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <button
                          onClick={() => handleEditDetail(detail.id, detail.content)}
                          className="w-5 h-5 rounded text-apple-gray-400 hover:text-apple-blue flex items-center justify-center"
                        >
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                        </button>
                        <button
                          onClick={() => onRemoveDetail(todo.id, detail.id)}
                          className="w-5 h-5 rounded text-apple-gray-400 hover:text-red-500 flex items-center justify-center"
                        >
                          <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  )}
                </li>
              ))}
            </ul>
          )}

          {/* 添加新明细 */}
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={newDetailContent}
              onChange={(e) => setNewDetailContent(e.target.value)}
              placeholder="添加新明细..."
              className="flex-1 px-3 py-2 text-xs border border-apple-gray-300 rounded-lg focus:ring-1 focus:ring-apple-blue focus:border-transparent outline-none"
              onKeyDown={(e) => {
                if (e.key === 'Enter') handleAddDetail();
              }}
            />
            <button
              onClick={handleAddDetail}
              disabled={!newDetailContent.trim()}
              className="px-3 py-2 bg-apple-blue text-white text-xs rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              添加
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
