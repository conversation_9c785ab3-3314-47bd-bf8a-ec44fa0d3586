'use client';

import { TodoItem as TodoItemType } from '@/types/todo';

interface TodoItemProps {
  todo: TodoItemType;
  isSelected: boolean;
  onToggle: (id: string) => void;
  onSelect: (id: string) => void;
  onDelete: (id: string) => void;
}

export default function TodoItem({ 
  todo, 
  isSelected, 
  onToggle, 
  onSelect, 
  onDelete 
}: TodoItemProps) {
  return (
    <div 
      className={`
        bg-white rounded-apple shadow-apple border border-apple-gray-200
        transition-all duration-200 ease-in-out cursor-pointer
        ${isSelected ? 'ring-2 ring-apple-blue ring-opacity-50 shadow-apple-lg' : 'hover:shadow-apple-lg'}
      `}
      onClick={() => onSelect(todo.id)}
    >
      <div className="p-4 flex items-center justify-between">
        <div className="flex items-center space-x-3 flex-1">
          {/* 苹果风格复选框 */}
          <button
            onClick={(e) => {
              e.stopPropagation();
              onToggle(todo.id);
            }}
            className={`
              w-6 h-6 rounded-full border-2 flex items-center justify-center
              transition-all duration-200 ease-in-out
              ${todo.completed 
                ? 'bg-apple-blue border-apple-blue' 
                : 'border-apple-gray-300 hover:border-apple-blue'
              }
            `}
          >
            {todo.completed && (
              <svg 
                className="w-3 h-3 text-white" 
                fill="currentColor" 
                viewBox="0 0 20 20"
              >
                <path 
                  fillRule="evenodd" 
                  d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" 
                  clipRule="evenodd" 
                />
              </svg>
            )}
          </button>
          
          {/* 事项名称 */}
          <span 
            className={`
              text-lg font-medium transition-all duration-200
              ${todo.completed 
                ? 'text-apple-gray-500 line-through' 
                : 'text-apple-gray-900'
              }
            `}
          >
            {todo.name}
          </span>
        </div>

        {/* 删除按钮 */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onDelete(todo.id);
          }}
          className="
            w-8 h-8 rounded-full bg-red-500 text-white
            flex items-center justify-center opacity-0 hover:opacity-100
            transition-opacity duration-200 ease-in-out
            hover:bg-red-600
          "
        >
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path 
              fillRule="evenodd" 
              d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
              clipRule="evenodd" 
            />
          </svg>
        </button>
      </div>

      {/* 明细展开区域 */}
      {isSelected && todo.details.length > 0 && (
        <div className="border-t border-apple-gray-200 px-4 py-3 bg-apple-gray-50">
          <h4 className="text-sm font-medium text-apple-gray-700 mb-2">明细：</h4>
          <ul className="space-y-1">
            {todo.details.map((detail, index) => (
              <li 
                key={index}
                className="text-sm text-apple-gray-600 flex items-start"
              >
                <span className="text-apple-blue font-medium mr-2">{index + 1}.</span>
                {detail}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
