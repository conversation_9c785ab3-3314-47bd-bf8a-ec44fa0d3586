'use client';

import { useState } from 'react';

interface AddTodoModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (name: string, details: string[]) => void;
}

export default function AddTodoModal({ isOpen, onClose, onAdd }: AddTodoModalProps) {
  const [name, setName] = useState('');
  const [details, setDetails] = useState<string[]>(['']);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      const filteredDetails = details.filter(detail => detail.trim() !== '');
      onAdd(name.trim(), filteredDetails);
      setName('');
      setDetails(['']);
      onClose();
    }
  };

  const addDetailField = () => {
    setDetails([...details, '']);
  };

  const updateDetail = (index: number, value: string) => {
    const newDetails = [...details];
    newDetails[index] = value;
    setDetails(newDetails);
  };

  const removeDetail = (index: number) => {
    if (details.length > 1) {
      setDetails(details.filter((_, i) => i !== index));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-apple shadow-apple-lg w-full max-w-md max-h-[80vh] overflow-hidden">
        {/* 头部 */}
        <div className="px-6 py-4 border-b border-apple-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-apple-gray-900">添加新事项</h2>
            <button
              onClick={onClose}
              className="w-8 h-8 rounded-full bg-apple-gray-200 text-apple-gray-600 hover:bg-apple-gray-300 flex items-center justify-center transition-colors"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>
        </div>

        {/* 内容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4 overflow-y-auto max-h-[60vh]">
          {/* 事项名称 */}
          <div>
            <label className="block text-sm font-medium text-apple-gray-700 mb-2">
              事项名称
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-4 py-3 border border-apple-gray-300 rounded-apple focus:ring-2 focus:ring-apple-blue focus:border-transparent outline-none transition-all"
              placeholder="输入事项名称..."
              autoFocus
            />
          </div>

          {/* 明细列表 */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-apple-gray-700">
                明细（可选）
              </label>
              <button
                type="button"
                onClick={addDetailField}
                className="text-apple-blue text-sm font-medium hover:text-blue-600 transition-colors"
              >
                + 添加明细
              </button>
            </div>
            <div className="text-xs text-apple-gray-500 mb-2">
              💡 提示：只有所有明细完成后，事项才会标记为完成
            </div>
            <div className="space-y-2">
              {details.map((detail, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <span className="text-apple-blue font-medium text-sm w-6">{index + 1}.</span>
                  <input
                    type="text"
                    value={detail}
                    onChange={(e) => updateDetail(index, e.target.value)}
                    className="flex-1 px-3 py-2 border border-apple-gray-300 rounded-lg focus:ring-2 focus:ring-apple-blue focus:border-transparent outline-none transition-all text-sm"
                    placeholder="输入明细内容..."
                  />
                  {details.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeDetail(index)}
                      className="w-6 h-6 rounded-full bg-red-500 text-white flex items-center justify-center hover:bg-red-600 transition-colors"
                    >
                      <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>
        </form>

        {/* 底部按钮 */}
        <div className="px-6 py-4 border-t border-apple-gray-200 flex space-x-3">
          <button
            type="button"
            onClick={onClose}
            className="flex-1 px-4 py-3 border border-apple-gray-300 text-apple-gray-700 rounded-apple hover:bg-apple-gray-50 transition-colors font-medium"
          >
            取消
          </button>
          <button
            onClick={handleSubmit}
            disabled={!name.trim()}
            className="flex-1 px-4 py-3 bg-apple-blue text-white rounded-apple hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium"
          >
            添加
          </button>
        </div>
      </div>
    </div>
  );
}
