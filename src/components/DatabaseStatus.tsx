'use client';

import { useState, useEffect } from 'react';
import { TodoService } from '@/lib/database';

interface DatabaseStatusProps {
  className?: string;
}

export default function DatabaseStatus({ className = '' }: DatabaseStatusProps) {
  const [stats, setStats] = useState({ total: 0, completed: 0, pending: 0 });
  const [storageInfo, setStorageInfo] = useState<{
    used: number;
    quota: number;
    percentage: number;
  } | null>(null);

  useEffect(() => {
    const loadStats = async () => {
      const todoStats = await TodoService.getStats();
      setStats(todoStats);
    };

    const loadStorageInfo = async () => {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        try {
          const estimate = await navigator.storage.estimate();
          const used = estimate.usage || 0;
          const quota = estimate.quota || 0;
          const percentage = quota > 0 ? (used / quota) * 100 : 0;
          
          setStorageInfo({
            used: Math.round(used / 1024 / 1024 * 100) / 100, // MB
            quota: Math.round(quota / 1024 / 1024 * 100) / 100, // MB
            percentage: Math.round(percentage * 100) / 100
          });
        } catch (error) {
          console.error('获取存储信息失败:', error);
        }
      }
    };

    loadStats();
    loadStorageInfo();

    // 定期更新统计信息
    const interval = setInterval(() => {
      loadStats();
      loadStorageInfo();
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const formatBytes = (bytes: number) => {
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / 1024 / 1024).toFixed(1)} MB`;
  };

  return (
    <div className={`bg-white rounded-apple shadow-apple p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-medium text-apple-gray-700 flex items-center">
          <svg className="w-4 h-4 mr-2 text-apple-blue" fill="currentColor" viewBox="0 0 20 20">
            <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
          </svg>
          IndexedDB 状态
        </h3>
        <div className="flex items-center text-xs text-apple-gray-500">
          <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
          已连接
        </div>
      </div>

      {/* 数据统计 */}
      <div className="grid grid-cols-3 gap-3 mb-4">
        <div className="text-center">
          <div className="text-lg font-semibold text-apple-gray-900">{stats.total}</div>
          <div className="text-xs text-apple-gray-600">总计</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-apple-blue">{stats.completed}</div>
          <div className="text-xs text-apple-gray-600">已完成</div>
        </div>
        <div className="text-center">
          <div className="text-lg font-semibold text-orange-500">{stats.pending}</div>
          <div className="text-xs text-apple-gray-600">待完成</div>
        </div>
      </div>

      {/* 存储使用情况 */}
      {storageInfo && (
        <div className="border-t border-apple-gray-200 pt-3">
          <div className="flex justify-between items-center mb-2">
            <span className="text-xs text-apple-gray-600">存储使用</span>
            <span className="text-xs text-apple-gray-600">
              {storageInfo.used} MB / {storageInfo.quota} MB
            </span>
          </div>
          <div className="w-full bg-apple-gray-200 rounded-full h-2">
            <div 
              className="bg-apple-blue h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(storageInfo.percentage, 100)}%` }}
            ></div>
          </div>
          <div className="text-xs text-apple-gray-500 mt-1 text-center">
            {storageInfo.percentage}% 已使用
          </div>
        </div>
      )}

      {/* 数据库信息 */}
      <div className="border-t border-apple-gray-200 pt-3 mt-3">
        <div className="text-xs text-apple-gray-500 space-y-1">
          <div className="flex justify-between">
            <span>数据库:</span>
            <span>TodoListDatabase</span>
          </div>
          <div className="flex justify-between">
            <span>版本:</span>
            <span>v1.0</span>
          </div>
          <div className="flex justify-between">
            <span>存储类型:</span>
            <span>IndexedDB</span>
          </div>
        </div>
      </div>
    </div>
  );
}
