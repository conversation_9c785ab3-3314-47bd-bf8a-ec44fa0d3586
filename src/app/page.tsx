'use client';

import { useState } from 'react';
import { useTodos } from '@/hooks/useTodos';
import TodoItem from '@/components/TodoItem';
import AddTodoModal from '@/components/AddTodoModal';

export default function Home() {
  const {
    todos,
    selectedTodo,
    selectedTodoId,
    setSelectedTodoId,
    addTodo,
    toggleTodo,
    deleteTodo
  } = useTodos();

  const [isAddModalOpen, setIsAddModalOpen] = useState(false);

  const handleSelectTodo = (id: string) => {
    setSelectedTodoId(selectedTodoId === id ? null : id);
  };

  return (
    <div className="min-h-screen bg-apple-gray-50">
      {/* 头部 */}
      <div className="bg-white shadow-apple border-b border-apple-gray-200">
        <div className="max-w-2xl mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-apple-gray-900 text-center">
            待办事项
          </h1>
          <p className="text-apple-gray-600 text-center mt-2">
            简洁优雅的苹果风格
          </p>
        </div>
      </div>

      {/* 主要内容 */}
      <div className="max-w-2xl mx-auto px-4 py-8">
        {/* 统计信息 */}
        <div className="bg-white rounded-apple shadow-apple p-4 mb-6">
          <div className="flex justify-between items-center text-sm">
            <span className="text-apple-gray-600">
              总计: <span className="font-semibold text-apple-gray-900">{todos.length}</span> 项
            </span>
            <span className="text-apple-gray-600">
              已完成: <span className="font-semibold text-apple-blue">
                {todos.filter(todo => todo.completed).length}
              </span> 项
            </span>
            <span className="text-apple-gray-600">
              待完成: <span className="font-semibold text-orange-500">
                {todos.filter(todo => !todo.completed).length}
              </span> 项
            </span>
          </div>
        </div>

        {/* 事项列表 */}
        <div className="space-y-3">
          {todos.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 mx-auto mb-4 bg-apple-gray-200 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-apple-gray-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-apple-gray-900 mb-2">
                暂无待办事项
              </h3>
              <p className="text-apple-gray-600 mb-4">
                点击下方按钮添加你的第一个待办事项
              </p>
            </div>
          ) : (
            todos.map(todo => (
              <TodoItem
                key={todo.id}
                todo={todo}
                isSelected={selectedTodoId === todo.id}
                onToggle={toggleTodo}
                onSelect={handleSelectTodo}
                onDelete={deleteTodo}
              />
            ))
          )}
        </div>
      </div>

      {/* 浮动添加按钮 */}
      <button
        onClick={() => setIsAddModalOpen(true)}
        className="
          fixed bottom-8 right-8 w-14 h-14 bg-apple-blue text-white
          rounded-full shadow-apple-lg hover:shadow-xl
          flex items-center justify-center
          transition-all duration-200 ease-in-out
          hover:bg-blue-600 hover:scale-105
          active:scale-95
        "
      >
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
        </svg>
      </button>

      {/* 添加事项模态框 */}
      <AddTodoModal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        onAdd={addTodo}
      />
    </div>
  );
}
